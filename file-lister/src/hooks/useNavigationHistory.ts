'use client';

import { useState, useCallback, useEffect } from 'react';

interface NavigationHistoryState {
  history: string[];
  currentIndex: number;
  canGoBack: boolean;
  canGoForward: boolean;
}

interface UseNavigationHistoryReturn {
  history: string[];
  currentIndex: number;
  canGoBack: boolean;
  canGoForward: boolean;
  pushPath: (path: string) => void;
  goBack: () => string | null;
  goForward: () => string | null;
  getCurrentPath: () => string;
  clearHistory: () => void;
  getBackPath: () => string | null;
  getForwardPath: () => string | null;
}

export function useNavigationHistory(initialPath: string = '/'): UseNavigationHistoryReturn {
  const [state, setState] = useState<NavigationHistoryState>(() => {
    // Initialize from URL if available
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      const pathFromUrl = urlParams.get('path');
      const startPath = pathFromUrl || initialPath;

      return {
        history: [startPath],
        currentIndex: 0,
        canGoBack: false,
        canGoForward: false,
      };
    }

    return {
      history: [initialPath],
      currentIndex: 0,
      canGoBack: false,
      canGoForward: false,
    };
  });

  // Update browser URL to match current path (non-blocking)
  const updateBrowserUrl = useCallback((path: string) => {
    // Use requestAnimationFrame to avoid setState during render
    requestAnimationFrame(() => {
      try {
        const url = new URL(window.location.href);
        url.searchParams.set('path', path);
        window.history.replaceState({ path }, '', url.toString());
      } catch (error) {
        console.error('Failed to update browser URL:', error);
      }
    });
  }, []);

  // Initialize from URL on mount
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const pathFromUrl = urlParams.get('path');

    if (pathFromUrl && pathFromUrl !== initialPath) {
      setState(prev => ({
        history: [pathFromUrl],
        currentIndex: 0,
        canGoBack: false,
        canGoForward: false,
      }));
    }
  }, [initialPath]);

  // Update can go back/forward flags whenever state changes
  const updateFlags = useCallback((newState: Omit<NavigationHistoryState, 'canGoBack' | 'canGoForward'>) => {
    return {
      ...newState,
      canGoBack: newState.currentIndex > 0,
      canGoForward: newState.currentIndex < newState.history.length - 1,
    };
  }, []);

  const pushPath = useCallback((path: string) => {
    setState(prev => {
      // Don't add if it's the same as current path
      if (prev.history[prev.currentIndex] === path) {
        return prev;
      }

      // Remove any forward history when pushing a new path
      const newHistory = prev.history.slice(0, prev.currentIndex + 1);
      newHistory.push(path);

      const newState = {
        history: newHistory,
        currentIndex: newHistory.length - 1,
      };

      // Update browser URL
      updateBrowserUrl(path);

      return updateFlags(newState);
    });
  }, [updateFlags, updateBrowserUrl]);

  const goBack = useCallback((): string | null => {
    let targetPath: string | null = null;

    setState(prev => {
      if (prev.currentIndex <= 0) {
        return prev;
      }

      const newIndex = prev.currentIndex - 1;
      targetPath = prev.history[newIndex];

      const newState = {
        history: prev.history,
        currentIndex: newIndex,
      };

      // Update browser URL
      if (targetPath) {
        updateBrowserUrl(targetPath);
      }

      return updateFlags(newState);
    });

    return targetPath;
  }, [updateFlags, updateBrowserUrl]);

  const goForward = useCallback((): string | null => {
    let targetPath: string | null = null;
    
    setState(prev => {
      if (prev.currentIndex >= prev.history.length - 1) {
        return prev;
      }

      const newIndex = prev.currentIndex + 1;
      targetPath = prev.history[newIndex];
      
      const newState = {
        history: prev.history,
        currentIndex: newIndex,
      };

      // Update browser URL
      if (targetPath) {
        updateBrowserUrl(targetPath);
      }

      return updateFlags(newState);
    });

    return targetPath;
  }, [updateFlags, updateBrowserUrl]);

  const getCurrentPath = useCallback((): string => {
    return state.history[state.currentIndex] || '/';
  }, [state.history, state.currentIndex]);

  const clearHistory = useCallback(() => {
    setState({
      history: ['/'],
      currentIndex: 0,
      canGoBack: false,
      canGoForward: false,
    });
    updateBrowserUrl('/');
  }, [updateBrowserUrl]);

  const getBackPath = useCallback((): string | null => {
    if (state.currentIndex <= 0) {
      return null;
    }
    return state.history[state.currentIndex - 1];
  }, [state.currentIndex, state.history]);

  const getForwardPath = useCallback((): string | null => {
    if (state.currentIndex >= state.history.length - 1) {
      return null;
    }
    return state.history[state.currentIndex + 1];
  }, [state.currentIndex, state.history]);

  return {
    history: state.history,
    currentIndex: state.currentIndex,
    canGoBack: state.canGoBack,
    canGoForward: state.canGoForward,
    pushPath,
    goBack,
    goForward,
    getCurrentPath,
    clearHistory,
    getBackPath,
    getForwardPath,
  };
}
