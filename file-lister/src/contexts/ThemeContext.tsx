'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { IconLibrary, FontFamily, ColorTheme, defaultTheme, colorThemes, fonts, ThemePreset } from '@/lib/themes';
import { loadFont, getFontFamily, preloadEssentialFonts } from '@/lib/fontLoader';
import {
  getPresets,
  createPreset,
  updatePreset,
  deletePreset,
  duplicatePreset,
  getPresetById,
  setCurrentPresetId,
  migrateCurrentTheme
} from '@/lib/presetManager';

interface ThemeContextType {
  iconLibrary: IconLibrary;
  font: FontFamily;
  colorTheme: ColorTheme;
  setIconLibrary: (library: IconLibrary) => void;
  setFont: (font: FontFamily) => void;
  setColorTheme: (theme: ColorTheme) => void;
  resetToDefaults: () => void;

  // Preset management
  presets: ThemePreset[];
  currentPresetId?: string;
  savePreset: (name: string) => ThemePreset;
  loadPreset: (presetId: string) => void;
  deletePreset: (presetId: string) => void;
  renamePreset: (presetId: string, newName: string) => void;
  duplicatePreset: (presetId: string, newName: string) => ThemePreset;
  refreshPresets: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

interface ThemeProviderProps {
  children: React.ReactNode;
}

export function ThemeProvider({ children }: ThemeProviderProps) {
  const [iconLibrary, setIconLibraryState] = useState<IconLibrary>(defaultTheme.iconLibrary);
  const [font, setFontState] = useState<FontFamily>(defaultTheme.font);
  const [colorTheme, setColorThemeState] = useState<ColorTheme>(defaultTheme.colorTheme);

  // Preset management state
  const [presets, setPresets] = useState<ThemePreset[]>([]);
  const [currentPresetId, setCurrentPresetIdState] = useState<string | undefined>();

  // Load theme from localStorage on mount and initialize font system
  useEffect(() => {
    // Initialize font loading system
    preloadEssentialFonts();

    // Load presets first
    const loadedPresets = getPresets();
    setPresets(loadedPresets);

    // Load current theme
    const savedTheme = localStorage.getItem('file-lister-theme');
    if (savedTheme) {
      try {
        const parsed = JSON.parse(savedTheme);
        setIconLibraryState(parsed.iconLibrary || defaultTheme.iconLibrary);
        setFontState(parsed.font || defaultTheme.font);
        setColorThemeState(parsed.colorTheme || defaultTheme.colorTheme);

        // Try to migrate current theme to a preset
        const migratedPreset = migrateCurrentTheme();
        if (migratedPreset) {
          setCurrentPresetIdState(migratedPreset.id);
          // Refresh presets to include the migrated one
          setPresets(getPresets());
        }
      } catch (error) {
        console.error('Failed to parse saved theme:', error);
      }
    } else {
      // No saved theme, try to find a matching default preset
      const matchingPreset = loadedPresets.find(p =>
        p.iconLibrary === defaultTheme.iconLibrary &&
        p.font === defaultTheme.font &&
        p.colorTheme === defaultTheme.colorTheme
      );
      if (matchingPreset) {
        setCurrentPresetIdState(matchingPreset.id);
      }
    }
  }, []);

  // Apply theme changes to document (optimized for performance)
  useEffect(() => {
    const theme = colorThemes[colorTheme];

    // Use requestAnimationFrame to avoid blocking the main thread
    requestAnimationFrame(() => {
      const root = document.documentElement;

      // Batch DOM updates for better performance
      const colorUpdates: [string, string][] = Object.entries(theme.colors);

      // Apply all color properties in a single batch
      colorUpdates.forEach(([key, value]) => {
        root.style.setProperty(`--color-${key}`, value);
      });

      // Apply theme class to body (optimized)
      const currentClasses = document.body.className.split(' ');
      const filteredClasses = currentClasses.filter(cls => !cls.startsWith('theme-'));
      document.body.className = [...filteredClasses, `theme-${colorTheme}`].join(' ');
    });

    // Debounce localStorage writes to avoid excessive I/O
    const saveTimeout = setTimeout(() => {
      localStorage.setItem('file-lister-theme', JSON.stringify({
        iconLibrary,
        font,
        colorTheme,
      }));
    }, 100);

    return () => clearTimeout(saveTimeout);
  }, [iconLibrary, font, colorTheme]);

  // Separate effect for font changes (non-blocking)
  useEffect(() => {
    // Use requestAnimationFrame for non-blocking font updates
    requestAnimationFrame(() => {
      const fontFamily = getFontFamily(font);
      const root = document.documentElement;

      // Apply font instantly (no async loading needed)
      root.style.setProperty('--font-family', fontFamily);
      document.body.style.fontFamily = fontFamily;
    });
  }, [font]);

  const setIconLibrary = (library: IconLibrary) => {
    setIconLibraryState(library);
  };

  const setFont = (newFont: FontFamily) => {
    setFontState(newFont);
  };

  const setColorTheme = (theme: ColorTheme) => {
    setColorThemeState(theme);
  };

  const resetToDefaults = () => {
    setIconLibraryState(defaultTheme.iconLibrary);
    setFontState(defaultTheme.font);
    setColorThemeState(defaultTheme.colorTheme);
    setCurrentPresetIdState(undefined);
  };

  // Preset management functions
  const savePreset = (name: string): ThemePreset => {
    try {
      const newPreset = createPreset(name, iconLibrary, font, colorTheme);
      setPresets(getPresets()); // Refresh presets
      setCurrentPresetIdState(newPreset.id);
      setCurrentPresetId(newPreset.id);
      return newPreset;
    } catch (error) {
      console.error('Failed to save preset:', error);
      throw error;
    }
  };

  const loadPreset = (presetId: string): void => {
    try {
      const preset = getPresetById(presetId);
      if (!preset) {
        throw new Error(`Preset with ID "${presetId}" not found`);
      }

      setIconLibraryState(preset.iconLibrary);
      setFontState(preset.font);
      setColorThemeState(preset.colorTheme);
      setCurrentPresetIdState(preset.id);
      setCurrentPresetId(preset.id);
    } catch (error) {
      console.error('Failed to load preset:', error);
      throw error;
    }
  };

  const deletePresetHandler = (presetId: string): void => {
    try {
      deletePreset(presetId);
      setPresets(getPresets()); // Refresh presets

      // Clear current preset if it was deleted
      if (currentPresetId === presetId) {
        setCurrentPresetIdState(undefined);
      }
    } catch (error) {
      console.error('Failed to delete preset:', error);
      throw error;
    }
  };

  const renamePreset = (presetId: string, newName: string): void => {
    try {
      updatePreset(presetId, { name: newName });
      setPresets(getPresets()); // Refresh presets
    } catch (error) {
      console.error('Failed to rename preset:', error);
      throw error;
    }
  };

  const duplicatePresetHandler = (presetId: string, newName: string): ThemePreset => {
    try {
      const newPreset = duplicatePreset(presetId, newName);
      setPresets(getPresets()); // Refresh presets
      return newPreset;
    } catch (error) {
      console.error('Failed to duplicate preset:', error);
      throw error;
    }
  };

  const refreshPresets = (): void => {
    setPresets(getPresets());
  };

  return (
    <ThemeContext.Provider
      value={{
        iconLibrary,
        font,
        colorTheme,
        setIconLibrary,
        setFont,
        setColorTheme,
        resetToDefaults,

        // Preset management
        presets,
        currentPresetId,
        savePreset,
        loadPreset,
        deletePreset: deletePresetHandler,
        renamePreset,
        duplicatePreset: duplicatePresetHandler,
        refreshPresets,
      }}
    >
      {children}
    </ThemeContext.Provider>
  );
}
