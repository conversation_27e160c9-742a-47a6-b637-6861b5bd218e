// Optimized font loading utility
// Uses Next.js preloaded fonts for instant switching without CORS issues

interface FontConfig {
  family: string;
  variable: string;
  category: 'sans' | 'mono';
}

// Font configurations matching Next.js font variables
const fontConfigs: Record<string, FontConfig> = {
  // Original sans-serif fonts
  'inter': {
    family: 'Inter',
    variable: '--font-inter',
    category: 'sans'
  },
  'roboto': {
    family: 'Roboto',
    variable: '--font-roboto',
    category: 'sans'
  },
  'poppins': {
    family: 'Poppins',
    variable: '--font-poppins',
    category: 'sans'
  },
  'open-sans': {
    family: 'Open Sans',
    variable: '--font-open-sans',
    category: 'sans'
  },
  'lato': {
    family: 'Lato',
    variable: '--font-lato',
    category: 'sans'
  },
  'montserrat': {
    family: 'Montserrat',
    variable: '--font-montserrat',
    category: 'sans'
  },
  'source-sans': {
    family: 'Source Sans 3',
    variable: '--font-source-sans',
    category: 'sans'
  },
  'nunito': {
    family: 'Nunito',
    variable: '--font-nunito',
    category: 'sans'
  },

  // New sans-serif fonts
  'work-sans': {
    family: 'Work Sans',
    variable: '--font-work-sans',
    category: 'sans'
  },
  'dm-sans': {
    family: 'DM Sans',
    variable: '--font-dm-sans',
    category: 'sans'
  },
  'plus-jakarta-sans': {
    family: 'Plus Jakarta Sans',
    variable: '--font-plus-jakarta-sans',
    category: 'sans'
  },
  'manrope': {
    family: 'Manrope',
    variable: '--font-manrope',
    category: 'sans'
  },
  'space-grotesk': {
    family: 'Space Grotesk',
    variable: '--font-space-grotesk',
    category: 'sans'
  },
  'outfit': {
    family: 'Outfit',
    variable: '--font-outfit',
    category: 'sans'
  },
  'lexend': {
    family: 'Lexend',
    variable: '--font-lexend',
    category: 'sans'
  },
  'public-sans': {
    family: 'Public Sans',
    variable: '--font-public-sans',
    category: 'sans'
  },

  // Original monospace fonts
  'jetbrains-mono': {
    family: 'JetBrains Mono',
    variable: '--font-jetbrains-mono',
    category: 'mono'
  },
  'fira-code': {
    family: 'Fira Code',
    variable: '--font-fira-code',
    category: 'mono'
  },
  'source-code-pro': {
    family: 'Source Code Pro',
    variable: '--font-source-code-pro',
    category: 'mono'
  },

  // New monospace fonts
  'roboto-mono': {
    family: 'Roboto Mono',
    variable: '--font-roboto-mono',
    category: 'mono'
  },
  'ubuntu-mono': {
    family: 'Ubuntu Mono',
    variable: '--font-ubuntu-mono',
    category: 'mono'
  },
  'ibm-plex-mono': {
    family: 'IBM Plex Mono',
    variable: '--font-ibm-plex-mono',
    category: 'mono'
  },
  'space-mono': {
    family: 'Space Mono',
    variable: '--font-space-mono',
    category: 'mono'
  }
};

/**
 * Instantly apply a preloaded font (no loading required)
 * All fonts are preloaded via Next.js in layout.tsx
 */
export async function loadFont(fontKey: string): Promise<void> {
  const config = fontConfigs[fontKey];
  if (!config) {
    console.warn(`Font configuration not found for: ${fontKey}`);
    return;
  }

  // All fonts are already loaded via Next.js, so this is instant
  console.log(`Font ready: ${config.family} (preloaded via Next.js)`);
  return Promise.resolve();
}

/**
 * Get the CSS font family string using Next.js CSS variables
 */
export function getFontFamily(fontKey: string): string {
  const config = fontConfigs[fontKey];
  if (!config) {
    return 'var(--font-inter), Inter, sans-serif'; // Default fallback
  }

  // Use CSS variable with appropriate fallback
  const fallback = config.category === 'mono' ? 'monospace' : 'sans-serif';

  return `var(${config.variable}), "${config.family}", ${fallback}`;
}

/**
 * Check if a font is available (all fonts are preloaded via Next.js)
 */
export function isFontLoaded(fontKey: string): boolean {
  return fontConfigs[fontKey] !== undefined;
}

/**
 * Initialize font system (all fonts are preloaded via Next.js)
 */
export function preloadEssentialFonts(): void {
  // All fonts are preloaded via Next.js layout, no action needed
  console.log('Font system initialized - all fonts preloaded via Next.js');
}

/**
 * Get all available font configurations
 */
export function getAllFonts(): Record<string, FontConfig> {
  return fontConfigs;
}
