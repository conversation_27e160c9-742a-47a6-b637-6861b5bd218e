'use client';

import React, { useState } from 'react';
import { ThemePreset } from '@/lib/themes';
import { Icon } from '@/components/Icon';

interface PresetCardProps {
  preset: ThemePreset;
  isActive: boolean;
  onLoad: (presetId: string) => void;
  onRename?: (presetId: string, newName: string) => void;
  onDuplicate?: (presetId: string, newName: string) => void;
  onDelete?: (presetId: string) => void;
}

export const PresetCard = React.memo(function PresetCard({
  preset,
  isActive,
  onLoad,
  onRename,
  onDuplicate,
  onDelete,
}: PresetCardProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editName, setEditName] = useState(preset.name);
  const [showActions, setShowActions] = useState(false);

  // No longer need theme config variables for simplified design

  const handleRename = () => {
    if (editName.trim() && editName.trim() !== preset.name && onRename) {
      try {
        onRename(preset.id, editName.trim());
        setIsEditing(false);
      } catch (error) {
        console.error('Failed to rename preset:', error);
        // Reset to original name on error
        setEditName(preset.name);
      }
    } else {
      setIsEditing(false);
      setEditName(preset.name);
    }
  };

  const handleDuplicate = () => {
    if (onDuplicate) {
      const newName = `${preset.name} Copy`;
      try {
        onDuplicate(preset.id, newName);
      } catch (error) {
        console.error('Failed to duplicate preset:', error);
      }
    }
  };

  const handleDelete = () => {
    if (onDelete && !preset.isDefault) {
      if (confirm(`Are you sure you want to delete "${preset.name}"?`)) {
        try {
          onDelete(preset.id);
        } catch (error) {
          console.error('Failed to delete preset:', error);
        }
      }
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleRename();
    } else if (e.key === 'Escape') {
      setIsEditing(false);
      setEditName(preset.name);
    }
  };

  return (
    <div
      className="relative px-4 py-3 rounded-lg border cursor-pointer transition-all duration-200 hover:shadow-md group"
      style={{
        borderColor: isActive ? 'var(--color-primary)' : 'var(--color-border)',
        backgroundColor: isActive ? 'var(--color-accent)' : 'var(--color-card)',
        color: isActive ? 'var(--color-accent-foreground)' : 'var(--color-card-foreground)',
      }}
      onClick={() => !isEditing && onLoad(preset.id)}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      {/* Default preset indicator - subtle */}
      {preset.isDefault && (
        <div
          className="absolute top-2 right-2 px-1.5 py-0.5 text-xs rounded opacity-60"
          style={{
            backgroundColor: 'var(--color-muted)',
            color: 'var(--color-muted-foreground)',
            fontSize: '10px',
          }}
        >
          default
        </div>
      )}

      {/* Action buttons */}
      {showActions && !preset.isDefault && (
        <div className="absolute top-2 right-2 flex space-x-1">
          {onRename && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                setIsEditing(true);
              }}
              className="p-1 rounded hover:bg-opacity-20"
              style={{ backgroundColor: 'var(--color-muted)' }}
              title="Rename"
            >
              <Icon name="type" size={12} />
            </button>
          )}
          {onDuplicate && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleDuplicate();
              }}
              className="p-1 rounded hover:bg-opacity-20"
              style={{ backgroundColor: 'var(--color-muted)' }}
              title="Duplicate"
            >
              <Icon name="file" size={12} />
            </button>
          )}
          {onDelete && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleDelete();
              }}
              className="p-1 rounded hover:bg-opacity-20"
              style={{ backgroundColor: 'var(--color-destructive)' }}
              title="Delete"
            >
              <Icon name="archive" size={12} />
            </button>
          )}
        </div>
      )}

      {/* Simplified content - preset name only */}
      <div className="flex items-center">
        <div className="flex-1 min-w-0">
          {isEditing ? (
            <input
              type="text"
              value={editName}
              onChange={(e) => setEditName(e.target.value)}
              onBlur={handleRename}
              onKeyDown={handleKeyDown}
              onClick={(e) => e.stopPropagation()}
              className="w-full bg-transparent border-b border-current outline-none font-medium text-base"
              style={{ color: 'inherit' }}
              autoFocus
            />
          ) : (
            <h3 className="font-medium text-base truncate" style={{ color: 'inherit' }}>
              {preset.name}
            </h3>
          )}
        </div>
      </div>

      {/* Active indicator */}
      {isActive && (
        <div
          className="absolute bottom-2 right-2 w-2 h-2 rounded-full"
          style={{ backgroundColor: 'var(--color-primary)' }}
        />
      )}
    </div>
  );
});
