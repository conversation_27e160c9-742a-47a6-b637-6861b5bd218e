'use client';

import React from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import { IconLibrary } from '@/lib/themes';

// Lucide Icons
import {
  Folder,
  FolderOpen,
  File,
  FileImage,
  FileVideo,
  FileAudio,
  FileText,
  FileCode,
  FileSpreadsheet,
  Archive,
  Settings,
  Palette,
  Type,
  Monitor,
  Home,
  ArrowLeft,
  ArrowRight,
  RefreshCw,
  Loader2,
} from 'lucide-react';

// Tabler Icons
import {
  IconFolder,
  IconFolderOpen,
  IconFile,
  IconFileTypeJpg,
  IconVideo,
  IconMusic,
  IconFileText,
  IconFileCode,
  IconFileSpreadsheet,
  IconArchive,
  IconSettings,
  IconPalette,
  IconTypography,
  IconDeviceDesktop,
  IconHome,
  IconArrowLeft,
  IconArrowRight,
  IconRefresh,
  IconLoader2,
} from '@tabler/icons-react';

// Heroicons
import {
  FolderIcon,
  FolderOpenIcon,
  DocumentIcon,
  PhotoIcon,
  VideoCameraIcon,
  MusicalNoteIcon,
  CodeBracketIcon,
  TableCellsIcon,
  ArchiveBoxIcon,
  Cog6ToothIcon,
  SwatchIcon,
  HomeIcon,
  ArrowLeftIcon,
  ArrowRightIcon,
  ArrowPathIcon,
} from '@heroicons/react/24/outline';

// Heroicons Solid
import {
  FolderIcon as FolderIconSolid,
  FolderOpenIcon as FolderOpenIconSolid,
  DocumentIcon as DocumentIconSolid,
  PhotoIcon as PhotoIconSolid,
  VideoCameraIcon as VideoCameraIconSolid,
  MusicalNoteIcon as MusicalNoteIconSolid,
  CodeBracketIcon as CodeBracketIconSolid,
  TableCellsIcon as TableCellsIconSolid,
  ArchiveBoxIcon as ArchiveBoxIconSolid,
  Cog6ToothIcon as Cog6ToothIconSolid,
  SwatchIcon as SwatchIconSolid,
  HomeIcon as HomeIconSolid,
  ArrowLeftIcon as ArrowLeftIconSolid,
  ArrowRightIcon as ArrowRightIconSolid,
  ArrowPathIcon as ArrowPathIconSolid,
} from '@heroicons/react/24/solid';

// React Icons (Font Awesome)
import {
  FaFolder,
  FaFolderOpen,
  FaFile,
  FaFileImage,
  FaFileVideo,
  FaFileAudio,
  FaFileLines,
  FaFileCode,
  FaTable,
  FaFileZipper,
  FaGear,
  FaPalette,
  FaFont,
  FaDesktop,
  FaHouse,
  FaArrowLeft,
  FaArrowRight,
  FaRotateRight,
  FaSpinner,
} from 'react-icons/fa6';

// Phosphor Icons
import {
  Folder as PhosphorFolder,
  FolderOpen as PhosphorFolderOpen,
  File as PhosphorFile,
  FileImage as PhosphorFileImage,
  FileVideo as PhosphorFileVideo,
  FileAudio as PhosphorFileAudio,
  FileText as PhosphorFileText,
  FileCode as PhosphorFileCode,
  Table as PhosphorTable,
  Archive as PhosphorArchive,
  Gear as PhosphorGear,
  Palette as PhosphorPalette,
  TextT as PhosphorTextT,
  Monitor as PhosphorMonitor,
  House as PhosphorHouse,
  ArrowLeft as PhosphorArrowLeft,
  ArrowRight as PhosphorArrowRight,
  ArrowClockwise as PhosphorArrowClockwise,
  Spinner as PhosphorSpinner,
} from 'phosphor-react';

// Feather Icons
import {
  Folder as FeatherFolder,
  File as FeatherFile,
  Image as FeatherImage,
  Video as FeatherVideo,
  Music as FeatherMusic,
  FileText as FeatherFileText,
  Code as FeatherCode,
  Grid as FeatherGrid,
  Archive as FeatherArchive,
  Settings as FeatherSettings,
  Droplet as FeatherDroplet,
  Type as FeatherType,
  Monitor as FeatherMonitor,
  Home as FeatherHome,
  ArrowLeft as FeatherArrowLeft,
  ArrowRight as FeatherArrowRight,
  RefreshCw as FeatherRefreshCw,
  Loader as FeatherLoader,
} from 'react-feather';

// Remix Icons
import {
  RiFolderLine,
  RiFolderOpenLine,
  RiFileLine,
  RiImageLine,
  RiVideoLine,
  RiMusicLine,
  RiFileTextLine,
  RiCodeLine,
  RiTableLine,
  RiArchiveLine,
  RiSettingsLine,
  RiPaletteLine,
  RiFontSize,
  RiComputerLine,
  RiHomeLine,
  RiArrowLeftLine,
  RiArrowRightLine,
  RiRefreshLine,
  RiLoader4Line,
} from '@remixicon/react';

// Material Icons - Temporarily disabled due to dependency issues
// import {
//   Folder as MaterialFolder,
//   FolderOpen as MaterialFolderOpen,
//   InsertDriveFile as MaterialFile,
//   Image as MaterialImage,
//   VideoFile as MaterialVideo,
//   AudioFile as MaterialAudio,
//   TextSnippet as MaterialTextSnippet,
//   Code as MaterialCode,
//   TableChart as MaterialTableChart,
//   Archive as MaterialArchive,
//   Settings as MaterialSettings,
//   Palette as MaterialPalette,
//   FormatSize as MaterialFormatSize,
//   Computer as MaterialComputer,
//   Home as MaterialHome,
//   ArrowBack as MaterialArrowBack,
//   ArrowForward as MaterialArrowForward,
//   Refresh as MaterialRefresh,
//   Sync as MaterialSync,
// } from '@mui/icons-material';

export type IconName =
  | 'folder' | 'folder-open' | 'file' | 'file-image' | 'file-video'
  | 'file-audio' | 'file-text' | 'file-code' | 'file-spreadsheet'
  | 'archive' | 'settings' | 'palette' | 'type' | 'monitor'
  | 'home' | 'arrow-left' | 'arrow-right' | 'refresh' | 'spinner';

interface IconProps {
  name: IconName;
  size?: number;
  className?: string;
  library?: IconLibrary;
}

const emojiIcons: Record<IconName, string> = {
  'folder': '📁',
  'folder-open': '📂',
  'file': '📄',
  'file-image': '🖼️',
  'file-video': '🎬',
  'file-audio': '🎵',
  'file-text': '📄',
  'file-code': '💻',
  'file-spreadsheet': '📊',
  'archive': '📦',
  'settings': '⚙️',
  'palette': '🎨',
  'type': '🔤',
  'monitor': '🖥️',
  'home': '🏠',
  'arrow-left': '⬅️',
  'arrow-right': '➡️',
  'refresh': '🔄',
  'spinner': '⏳',
};

export function Icon({ name, size = 20, className = '', library }: IconProps) {
  const { iconLibrary } = useTheme();
  const activeLibrary = library || iconLibrary;

  const iconProps = {
    size,
    className,
    style: { width: size, height: size },
  };

  if (activeLibrary === 'emoji') {
    return (
      <span 
        className={`inline-block ${className}`}
        style={{ fontSize: size }}
      >
        {emojiIcons[name]}
      </span>
    );
  }

  const iconMap = {
    lucide: {
      'folder': <Folder {...iconProps} />,
      'folder-open': <FolderOpen {...iconProps} />,
      'file': <File {...iconProps} />,
      'file-image': <FileImage {...iconProps} />,
      'file-video': <FileVideo {...iconProps} />,
      'file-audio': <FileAudio {...iconProps} />,
      'file-text': <FileText {...iconProps} />,
      'file-code': <FileCode {...iconProps} />,
      'file-spreadsheet': <FileSpreadsheet {...iconProps} />,
      'archive': <Archive {...iconProps} />,
      'settings': <Settings {...iconProps} />,
      'palette': <Palette {...iconProps} />,
      'type': <Type {...iconProps} />,
      'monitor': <Monitor {...iconProps} />,
      'home': <Home {...iconProps} />,
      'arrow-left': <ArrowLeft {...iconProps} />,
      'arrow-right': <ArrowRight {...iconProps} />,
      'refresh': <RefreshCw {...iconProps} />,
      'spinner': <Loader2 {...iconProps} />,
    },
    tabler: {
      'folder': <IconFolder {...iconProps} />,
      'folder-open': <IconFolderOpen {...iconProps} />,
      'file': <IconFile {...iconProps} />,
      'file-image': <IconFileTypeJpg {...iconProps} />,
      'file-video': <IconVideo {...iconProps} />,
      'file-audio': <IconMusic {...iconProps} />,
      'file-text': <IconFileText {...iconProps} />,
      'file-code': <IconFileCode {...iconProps} />,
      'file-spreadsheet': <IconFileSpreadsheet {...iconProps} />,
      'archive': <IconArchive {...iconProps} />,
      'settings': <IconSettings {...iconProps} />,
      'palette': <IconPalette {...iconProps} />,
      'type': <IconTypography {...iconProps} />,
      'monitor': <IconDeviceDesktop {...iconProps} />,
      'home': <IconHome {...iconProps} />,
      'arrow-left': <IconArrowLeft {...iconProps} />,
      'arrow-right': <IconArrowRight {...iconProps} />,
      'refresh': <IconRefresh {...iconProps} />,
      'spinner': <IconLoader2 {...iconProps} />,
    },
    heroicons: {
      'folder': <FolderIcon {...iconProps} />,
      'folder-open': <FolderOpenIcon {...iconProps} />,
      'file': <DocumentIcon {...iconProps} />,
      'file-image': <PhotoIcon {...iconProps} />,
      'file-video': <VideoCameraIcon {...iconProps} />,
      'file-audio': <MusicalNoteIcon {...iconProps} />,
      'file-text': <DocumentIcon {...iconProps} />,
      'file-code': <CodeBracketIcon {...iconProps} />,
      'file-spreadsheet': <TableCellsIcon {...iconProps} />,
      'archive': <ArchiveBoxIcon {...iconProps} />,
      'settings': <Cog6ToothIcon {...iconProps} />,
      'palette': <SwatchIcon {...iconProps} />,
      'type': <DocumentIcon {...iconProps} />,
      'monitor': <DocumentIcon {...iconProps} />,
      'home': <HomeIcon {...iconProps} />,
      'arrow-left': <ArrowLeftIcon {...iconProps} />,
      'arrow-right': <ArrowRightIcon {...iconProps} />,
      'refresh': <ArrowPathIcon {...iconProps} />,
      'spinner': <ArrowPathIcon {...iconProps} />,
    },
    'heroicons-solid': {
      'folder': <FolderIconSolid {...iconProps} />,
      'folder-open': <FolderOpenIconSolid {...iconProps} />,
      'file': <DocumentIconSolid {...iconProps} />,
      'file-image': <PhotoIconSolid {...iconProps} />,
      'file-video': <VideoCameraIconSolid {...iconProps} />,
      'file-audio': <MusicalNoteIconSolid {...iconProps} />,
      'file-text': <DocumentIconSolid {...iconProps} />,
      'file-code': <CodeBracketIconSolid {...iconProps} />,
      'file-spreadsheet': <TableCellsIconSolid {...iconProps} />,
      'archive': <ArchiveBoxIconSolid {...iconProps} />,
      'settings': <Cog6ToothIconSolid {...iconProps} />,
      'palette': <SwatchIconSolid {...iconProps} />,
      'type': <DocumentIconSolid {...iconProps} />,
      'monitor': <DocumentIconSolid {...iconProps} />,
      'home': <HomeIconSolid {...iconProps} />,
      'arrow-left': <ArrowLeftIconSolid {...iconProps} />,
      'arrow-right': <ArrowRightIconSolid {...iconProps} />,
      'refresh': <ArrowPathIconSolid {...iconProps} />,
      'spinner': <ArrowPathIconSolid {...iconProps} />,
    },
    'react-icons': {
      'folder': <FaFolder {...iconProps} />,
      'folder-open': <FaFolderOpen {...iconProps} />,
      'file': <FaFile {...iconProps} />,
      'file-image': <FaFileImage {...iconProps} />,
      'file-video': <FaFileVideo {...iconProps} />,
      'file-audio': <FaFileAudio {...iconProps} />,
      'file-text': <FaFileLines {...iconProps} />,
      'file-code': <FaFileCode {...iconProps} />,
      'file-spreadsheet': <FaTable {...iconProps} />,
      'archive': <FaFileZipper {...iconProps} />,
      'settings': <FaGear {...iconProps} />,
      'palette': <FaPalette {...iconProps} />,
      'type': <FaFont {...iconProps} />,
      'monitor': <FaDesktop {...iconProps} />,
      'home': <FaHouse {...iconProps} />,
      'arrow-left': <FaArrowLeft {...iconProps} />,
      'arrow-right': <FaArrowRight {...iconProps} />,
      'refresh': <FaRotateRight {...iconProps} />,
      'spinner': <FaSpinner {...iconProps} />,
    },
    phosphor: {
      'folder': <PhosphorFolder {...iconProps} />,
      'folder-open': <PhosphorFolderOpen {...iconProps} />,
      'file': <PhosphorFile {...iconProps} />,
      'file-image': <PhosphorFileImage {...iconProps} />,
      'file-video': <PhosphorFileVideo {...iconProps} />,
      'file-audio': <PhosphorFileAudio {...iconProps} />,
      'file-text': <PhosphorFileText {...iconProps} />,
      'file-code': <PhosphorFileCode {...iconProps} />,
      'file-spreadsheet': <PhosphorTable {...iconProps} />,
      'archive': <PhosphorArchive {...iconProps} />,
      'settings': <PhosphorGear {...iconProps} />,
      'palette': <PhosphorPalette {...iconProps} />,
      'type': <PhosphorTextT {...iconProps} />,
      'monitor': <PhosphorMonitor {...iconProps} />,
      'home': <PhosphorHouse {...iconProps} />,
      'arrow-left': <PhosphorArrowLeft {...iconProps} />,
      'arrow-right': <PhosphorArrowRight {...iconProps} />,
      'refresh': <PhosphorArrowClockwise {...iconProps} />,
      'spinner': <PhosphorSpinner {...iconProps} />,
    },
    feather: {
      'folder': <FeatherFolder {...iconProps} />,
      'folder-open': <FeatherFolder {...iconProps} />,
      'file': <FeatherFile {...iconProps} />,
      'file-image': <FeatherImage {...iconProps} />,
      'file-video': <FeatherVideo {...iconProps} />,
      'file-audio': <FeatherMusic {...iconProps} />,
      'file-text': <FeatherFileText {...iconProps} />,
      'file-code': <FeatherCode {...iconProps} />,
      'file-spreadsheet': <FeatherGrid {...iconProps} />,
      'archive': <FeatherArchive {...iconProps} />,
      'settings': <FeatherSettings {...iconProps} />,
      'palette': <FeatherDroplet {...iconProps} />,
      'type': <FeatherType {...iconProps} />,
      'monitor': <FeatherMonitor {...iconProps} />,
      'home': <FeatherHome {...iconProps} />,
      'arrow-left': <FeatherArrowLeft {...iconProps} />,
      'arrow-right': <FeatherArrowRight {...iconProps} />,
      'refresh': <FeatherRefreshCw {...iconProps} />,
      'spinner': <FeatherLoader {...iconProps} />,
    },
    remix: {
      'folder': <RiFolderLine {...iconProps} />,
      'folder-open': <RiFolderOpenLine {...iconProps} />,
      'file': <RiFileLine {...iconProps} />,
      'file-image': <RiImageLine {...iconProps} />,
      'file-video': <RiVideoLine {...iconProps} />,
      'file-audio': <RiMusicLine {...iconProps} />,
      'file-text': <RiFileTextLine {...iconProps} />,
      'file-code': <RiCodeLine {...iconProps} />,
      'file-spreadsheet': <RiTableLine {...iconProps} />,
      'archive': <RiArchiveLine {...iconProps} />,
      'settings': <RiSettingsLine {...iconProps} />,
      'palette': <RiPaletteLine {...iconProps} />,
      'type': <RiFontSize {...iconProps} />,
      'monitor': <RiComputerLine {...iconProps} />,
      'home': <RiHomeLine {...iconProps} />,
      'arrow-left': <RiArrowLeftLine {...iconProps} />,
      'arrow-right': <RiArrowRightLine {...iconProps} />,
      'refresh': <RiRefreshLine {...iconProps} />,
      'spinner': <RiLoader4Line {...iconProps} />,
    },
    // material: {
    //   'folder': <MaterialFolder {...iconProps} />,
    //   'folder-open': <MaterialFolderOpen {...iconProps} />,
    //   'file': <MaterialFile {...iconProps} />,
    //   'file-image': <MaterialImage {...iconProps} />,
    //   'file-video': <MaterialVideo {...iconProps} />,
    //   'file-audio': <MaterialAudio {...iconProps} />,
    //   'file-text': <MaterialTextSnippet {...iconProps} />,
    //   'file-code': <MaterialCode {...iconProps} />,
    //   'file-spreadsheet': <MaterialTableChart {...iconProps} />,
    //   'archive': <MaterialArchive {...iconProps} />,
    //   'settings': <MaterialSettings {...iconProps} />,
    //   'palette': <MaterialPalette {...iconProps} />,
    //   'type': <MaterialFormatSize {...iconProps} />,
    //   'monitor': <MaterialComputer {...iconProps} />,
    //   'home': <MaterialHome {...iconProps} />,
    //   'arrow-left': <MaterialArrowBack {...iconProps} />,
    //   'arrow-right': <MaterialArrowForward {...iconProps} />,
    //   'refresh': <MaterialRefresh {...iconProps} />,
    //   'spinner': <MaterialSync {...iconProps} />,
    // }, // Temporarily disabled due to dependency issues
  };

  return iconMap[activeLibrary]?.[name] || iconMap.lucide[name];
}
